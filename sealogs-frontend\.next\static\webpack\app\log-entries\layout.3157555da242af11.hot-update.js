"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/layout",{

/***/ "(app-pages-browser)/./src/app/lib/graphQL/query/logEntrySections/TripReport_LogBookEntrySection_Brief.ts":
/*!********************************************************************************************!*\
  !*** ./src/app/lib/graphQL/query/logEntrySections/TripReport_LogBookEntrySection_Brief.ts ***!
  \********************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TripReport_LogBookEntrySection_Brief: function() { return /* binding */ TripReport_LogBookEntrySection_Brief; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"(app-pages-browser)/./node_modules/.pnpm/@swc+helpers@0.5.5/node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var graphql_tag__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! graphql-tag */ \"(app-pages-browser)/./node_modules/.pnpm/graphql-tag@2.12.6_graphql@16.11.0/node_modules/graphql-tag/lib/index.js\");\n\nfunction _templateObject() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n    query GetTripReport_LogBookEntrySections_Brief($id: [ID]!) {\\n        readTripReport_LogBookEntrySections(\\n            filter: { id: { in: $id } }\\n            sort: { created: DESC }\\n        ) {\\n            nodes {\\n                id\\n                archived\\n                created\\n                depart\\n                lastEdited\\n                departFrom\\n                arrive\\n                arriveTo\\n                departTime\\n                fromLat\\n                fromLong\\n                arriveTime\\n                totalVehiclesCarried\\n                toLat\\n                toLong\\n                pob\\n                totalPOB\\n                totalGuests\\n                totalPaxJoined\\n                totalVehiclesJoined\\n                comment\\n                dangerousGoodsRecords {\\n                    nodes {\\n                        id\\n                        comment\\n                        type\\n                    }\\n                }\\n                tripEvents(sort: { created: ASC }) {\\n                    nodes {\\n                        id\\n                        eventCategory\\n                        created\\n                        eventType_PassengerDropFacilityID\\n                        eventType_TaskingID\\n                        eventType_PassengerDropFacility {\\n                            id\\n                            fuelLevel\\n                            fuelLog {\\n                                nodes {\\n                                    id\\n                                    fuelAdded\\n                                    fuelBefore\\n                                    fuelAfter\\n                                    date\\n                                    costPerLitre\\n                                    totalCost\\n                                    fuelTank {\\n                                        id\\n                                        capacity\\n                                        safeFuelCapacity\\n                                        currentLevel\\n                                        title\\n                                    }\\n                                }\\n                            }\\n                        }\\n                        tripUpdate {\\n                            id\\n                            date\\n                            title\\n                            lat\\n                            long\\n                            notes\\n                            attachment {\\n                                nodes {\\n                                    id\\n                                    title\\n                                }\\n                            }\\n                            geoLocationID\\n                            geoLocation {\\n                                id\\n                                title\\n                                lat\\n                                long\\n                            }\\n                        }\\n                        eventType_RefuellingBunkering {\\n                            id\\n                            date\\n                            title\\n                            lat\\n                            long\\n                            notes\\n                            geoLocationID\\n                            geoLocation {\\n                                id\\n                                title\\n                                lat\\n                                long\\n                            }\\n                            fuelLog {\\n                                nodes {\\n                                    id\\n                                    fuelAdded\\n                                    fuelBefore\\n                                    fuelAfter\\n                                    date\\n                                    costPerLitre\\n                                    totalCost\\n                                    fuelTank {\\n                                        id\\n                                        capacity\\n                                        safeFuelCapacity\\n                                        currentLevel\\n                                        title\\n                                    }\\n                                }\\n                            }\\n                        }\\n                        eventType_Tasking {\\n                            id\\n                            fuelLevel\\n                            type\\n                            cgop\\n                            sarop\\n                            pausedTaskID\\n                            openTaskID\\n                            completedTaskID\\n                            groupID\\n                            parentTaskingID\\n                            towingChecklist {\\n                                id\\n                            }\\n                            fuelLog {\\n                                nodes {\\n                                    id\\n                                    fuelAdded\\n                                    fuelBefore\\n                                    fuelAfter\\n                                    date\\n                                    costPerLitre\\n                                    totalCost\\n                                    fuelTank {\\n                                        id\\n                                        capacity\\n                                        safeFuelCapacity\\n                                        currentLevel\\n                                        title\\n                                    }\\n                                }\\n                            }\\n                        }\\n                    }\\n                }\\n                fromLocation {\\n                    id\\n                    title\\n                    lat\\n                    long\\n                }\\n                toLocation {\\n                    id\\n                    title\\n                    lat\\n                    long\\n                }\\n            }\\n        }\\n    }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\n\nconst TripReport_LogBookEntrySection_Brief = (0,graphql_tag__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_templateObject());\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/lib/graphQL/query/logEntrySections/TripReport_LogBookEntrySection_Brief.ts\n"));

/***/ })

});